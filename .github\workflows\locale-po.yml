name: Compile po into mo

on:
  push:
    branches:
      - main
      - dev_locale
    paths:
      - '**/*.po'
      - '**/locale-po.yml'

jobs:
  job1:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: pip install polib==1.2.0

      - name: Show dir
        run: pwd

      - name: Compile PO files
        run: |
          PYTHONPATH=$(pwd)/src python src/one_dragon/devtools/compile_po.py

      - name: Commit changes
        run: |
          if ! git diff --quiet; then
            git config user.name github-actions
            git config user.email <EMAIL>
            git add assets/text/output/**/*.mo
            git commit -m "#2 自动编译mo文件提交 $(date +%Y-%m-%d)"
            git push
          else
            echo "No file modifications"
          fi