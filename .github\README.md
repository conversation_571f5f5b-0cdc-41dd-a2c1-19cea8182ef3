<!-- markdownlint-restore -->
<div align="center">

<img src="https://socialify.git.ci/DoctorReid/ZenlessZoneZero-OneDragon/image?description=1&font=Inter&language=1&logo=https%3A%2F%2Fgithub.com%2FDoctorReid%2FZenlessZoneZero-OneDragon%2Fblob%2Fmain%2F.github%2Fimage%2Fellen.png%3Fraw%3Dtrue&name=1&owner=1&pattern=Charlie+Brown&theme=Light" alt="ZenlessZoneZero-OneDragon"/>
<div>

---

</div>

__ZenlessZoneZero-OneDragon__


<div>
    <img alt="platform" src="https://img.shields.io/badge/platform-Windows-blueviolet">
    <img alt="commit" src="https://img.shields.io/github/commit-activity/m/DoctorReid/ZenlessZoneZero-OneDragon?color=blue">
</div>
<div>
    <img alt="stars" src="https://img.shields.io/github/stars/DoctorReid/ZenlessZoneZero-OneDragon?style=social">
    <img alt="GitHub all releases" src="https://img.shields.io/github/downloads/DoctorReid/ZenlessZoneZero-OneDragon/total?style=social">
</div>
<br>

学习实践 __图像识别 & 自动化__ 的智能解决方案

✨ 如果喜欢本项目，欢迎右上角点亮`Star`支持 ✨

</div>

## 🚀 功能介绍

<div align="left">

🤖 __自动战斗__：支持自定义逻辑、技能标记、条件、变量等进阶用法  
🛡️ __闪避助手__：基于声音、图像一体化模型识别，轻占用，高准确  
🧹 __日常清理__：影像店、刮刮乐、咖啡店、材料、奖励等全日常清理  
☕ __空洞作战__：基于大模型训练的识别、事件、寻路的功能模块  
⏲️ __更多功能__： 定时启动、多账号切换、定时任务、自动对话……

</div>

## 📸 页面预览

<div align="center">

![App Interface](./image/app.png#gh-light-mode-only "操作界面")
*✨ 简洁直观的操作界面 ✨*

</div>

## 🛠️ 快速开始

🔧 [新手入门指南](https://onedragon-anything.github.io/zzz/zh/quickstart.html)  
✨ [一条龙官网](https://onedragon-anything.github.io/)

## ⚠️ 免责声明

> 📌 本项目仅供学习交流，开发者团队保留最终解释权  
> 🎨 项目LOGO版权归[巡夜子](https://github.com/yokuminto)及全体开发者所有  
> ⚖️ 使用本工具产生的一切风险需自行承担  
> 🚫 本项目未授权任何个人、商家、自媒体账号等进行售卖  
> 🚫 若您遇到商家使用本软件进行代练并收费，产生的任何问题及后果与本软件无关  
> 🚫 开发者团队不会为您提供任何"售后"服务

## 🌟 贡献者们

<div>

感谢参与项目的所有贡献者 ([完整名单](https://github.com/DoctorReid/ZenlessZoneZero-OneDragon/graphs/contributors))：

![Contributors](https://contrib.rocks/image?repo=DoctorReid/ZenlessZoneZero-OneDragon&columns=12)

是你们的参与共同构建了这个项目，让这个项目越来越好♡  
如果想要参与开发，可以参考 [一条龙官网](https://onedragon-anything.github.io/) 对应的开发指南，我们期待你的加入

</div>

## 📢 加入社区

<div>

🔗 **官方QQ群组**  
👉 主群：`861603314`(请参阅下方内容)   
👶 新手村：`925199190`

💬 [问题反馈](https://github.com/DoctorReid/ZenlessZoneZero-OneDragon/issues)

> 申请主群，需要点星星，并填入Github账号的ID作为入群申请 (右上角头像 -> Your Profile，网址上显示的ID)，请遵守群规，友善交流。



</div>

## ☕ 支持我们

<div align="center">

如果本项目为您带来便利，欢迎支持服务器维护：

<img src="./image/sponsor.png" width = "480" alt="图片名称" align=center />

💖 查看[致谢名单](https://onedragon-anything.github.io/other/zh/like/thanks_2024.html)

</div>
