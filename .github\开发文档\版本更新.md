## 1.新角色

- agent.py AgentEnum 增加角色，注意角色名称需要与空洞里呼叫支援出现的名字一致
- 头像截图
    - 战斗画面1号位，2号位，快速支援，连携技
    - 如果需要状态判断，则需要3人组队和2人组队各位置的状态截图
    - 空洞下方头像
    - 预备编队1号位头像
- call_for_support.py reject_agent增加拒绝选项
- 呼叫增援-拒绝.yml 增加拒绝选项
- 邂逅.yml 增加空洞事件

### 1.1.角色状态

增加后 可在[测试项目](https://github.com/DoctorReid/zzz-od-test/tree/main/test/auto_battle/agent_state_checker)增加对应测试

#### 1.1.1.原理

通常情况下，角色的状态都有一个固定的位置，在此基础上显示长条、圆点之类的内容。

因此，识别的最简单方法就是对固定位置进行颜色判断。

增加一个角色状态，需要做以下内容

- 在AgentEnum增加状态定义
- 每个状态，需要截图，3人组队和2人组队各个位置的截图，根据截图在模板管理中增加对应的模板
- 在测试项目中，增加对应的测试样例

#### 1.1.1.长条类-1

例子：青衣、莱特

可以通过长条的背景色，来判断未满的状态条长度，从而反推当前的状态条长度。

在模板管理中，增加高度为1的模板，模板记录的位置就是需要识颜色的位置。

识别代码见 agent_state_checker.check_length_by_background_gray


## 2.新皮肤

- agent_outfit_config.py 增加对应皮肤选项
- 按照 1.新角色 对新皮肤进行截图
- zzz_one_dragon_setting_interface.py 
  - get_agent_outfit_group 增加选项
  - on_interface_shown 增加初始化
- zzz_context.py init_agent_template_id 增加对应初始化

## 3.