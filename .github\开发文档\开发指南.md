# 1.开发环境

## 1.1.uv

项目适用 uv 进行环境管理，[下载地址](https://github.com/astral-sh/uv/releases/latest)

## 1.2.环境安装

普通使用

```shell
uv sync
```

开发使用

```shell
uv sync --group dev
```

# 2.打包

进入 deploy 文件夹，运行 `build_full.bat`

## 2.1.安装器

生成spec文件并打包

```shell
uv run pyinstaller --onefile --windowed --uac-admin --icon="../assets/ui/installer_logo.ico" --add-data "../config/project.yml;config" ../src/zzz_od/gui/zzz_installer.py -n "OneDragon Installer"
```

使用spec打包

```shell
uv run pyinstaller "OneDragon Installer.spec"
```

## 2.2.完整运行器

生成spec文件并打包

```shell
uv run pyinstaller --onefile --uac-admin --icon="../assets/ui/zzz_logo.ico" ../src/zzz_od/win_exe/full_launcher.py -n "OneDragon Launcher"
```

使用spec打包
```shell
uv run pyinstaller "OneDragon Launcher.spec"
```

## 2.3.一条龙运行器

生成spec文件并打包

```shell
uv run pyinstaller --onefile --uac-admin --icon="../assets/ui/scheduler_logo.ico" ../src/zzz_od/win_exe/scheduler_launcher.py -n "OneDragon Scheduler"
```

使用spec打包
```shell
uv run pyinstaller "OneDragon Scheduler.spec"
```

